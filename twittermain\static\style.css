/* Modern Simplified CSS */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  min-height: 100vh;
  margin: 0;
  padding: 0;
  color: #1f2937;
}

/* Typography */
.display-4 {
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  margin-bottom: 2rem;
  font-size: 2.5rem;
  text-align: center;
  padding: 2rem 0;
}

h2 {
  color: white;
  font-weight: 600;
  font-size: 1.5rem;
  text-align: center;
  margin: 2rem 0 1.5rem;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 0;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: white !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  margin: 0 0.25rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.navbar-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white !important;
}

.navbar-nav .nav-link.active {
  background: rgba(255, 255, 255, 0.25) !important;
  color: white !important;
}

/* Form Controls */
.form-control {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.form-control:focus {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
  color: white;
}

/* Buttons */
.btn {
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  text-transform: none;
  letter-spacing: normal;
}

.btn-primary {
  background: #6366f1;
  color: white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
  background: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
}

.btn-danger {
  background: #ef4444;
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.btn-warning {
  background: #f59e0b !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
}

.btn-warning:hover {
  background: #d97706 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4) !important;
}

.btn-outline-success {
  border: 2px solid rgba(255, 255, 255, 0.5);
  color: white;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-outline-success:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.7);
  color: white;
}

.btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.5);
  color: white;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.7);
  color: white;
}

/* User Dropdown Styles */
.user-dropdown {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.5rem 1rem !important;
  transition: all 0.2s ease;
}

.user-dropdown:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white !important;
}

.username {
  font-weight: 500;
  margin-left: 0.25rem;
}

.dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  margin-top: 0.5rem;
  min-width: 200px;
}

.dropdown-header {
  color: #6b7280 !important;
  font-size: 0.85rem;
  padding: 0.5rem 1rem;
  margin-bottom: 0;
}

.dropdown-item {
  color: #374151;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.dropdown-item:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.logout-btn {
  color: #ef4444 !important;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #dc2626 !important;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.container.row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
}

/* Cards */
.card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  overflow: hidden;
  width: 20rem;
  margin-bottom: 2rem;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15);
}

.card-img-top {
  height: 200px;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.card:hover .card-img-top {
  transform: scale(1.05);
}

.card-body {
  padding: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.card-text {
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.card-body .btn {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Forms */
form {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 2rem;
  margin: 1.5rem auto;
  max-width: 600px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

form label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

form input[type="text"],
form input[type="email"],
form input[type="password"],
form input[type="url"],
form input[type="file"],
form textarea,
form select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  color: #1f2937;
  background: white;
  transition: all 0.2s ease;
  margin-bottom: 1rem;
}

form input:focus,
form textarea:focus,
form select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

form textarea {
  min-height: 100px;
  resize: vertical;
}

form p {
  margin-bottom: 1.5rem;
}

/* Links */
a {
  color: #6366f1;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #4f46e5;
}

h2 a {
  display: inline-block;
  color: rgba(255, 255, 255, 0.9);
  padding: 0.5rem 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

h2 a:hover {
  color: white;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
}

/* Error Messages */
.errorlist {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  list-style: none;
  color: #dc2626;
}

.errorlist li {
  margin: 0;
  padding: 0.25rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container.row {
    flex-direction: column;
    align-items: center;
  }

  .card {
    width: 100%;
    max-width: 400px;
  }

  .display-4 {
    font-size: 2rem;
    padding: 1rem 0;
  }

  form {
    padding: 1.5rem;
    margin: 1rem;
  }

  h2 {
    font-size: 1.3rem;
    margin: 1rem 0;
  }
}

@media (max-width: 480px) {
  .card-body {
    padding: 1rem;
  }

  .btn {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
  }

  form {
    padding: 1rem;
  }
}

/* Utilities */
.text-center {
  text-align: center;
}

.d-flex {
  display: flex;
}

.gap-2 {
  gap: 0.5rem;
}
