{% load static %}

<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="{% static 'style.css' %}">
  <title>
    {% block title %}
      Twitter
    {% endblock %}
  </title>
</head>
<body>
  <nav class="navbar navbar-expand-lg bg-body-tertiary">
  <div class="container-fluid">
    <a class="navbar-brand" href="{% url 'tweet_list' %}">
      <i class="fas fa-twitter me-2"></i>Twitter
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav me-auto mb-2 mb-lg-0">
        <li class="nav-item">
          <a class="nav-link" href="{% url 'tweet_list' %}">
            <i class="fas fa-home me-1"></i>Home
          </a>
        </li>
        {% if user.is_authenticated %}
        <li class="nav-item">
          <a class="nav-link" href="{% url 'tweet_create' %}">
            <i class="fas fa-plus me-1"></i>Create Tweet
          </a>
        </li>
        {% endif %}
      </ul>

      <!-- User Authentication Section -->
      <div class="navbar-nav">
        {% if user.is_authenticated %}
          <div class="nav-item user-menu">
            <div class="user-dropdown-trigger">
              <i class="fas fa-user-circle me-1"></i>
              <span class="username">{{ user.username }}</span>
              <i class="fas fa-chevron-down ms-1"></i>
            </div>
            <div class="user-dropdown-menu">
              <div class="dropdown-header">Signed in as <strong>{{ user.username }}</strong></div>
              <div class="dropdown-divider"></div>
              <form method="post" action="{% url 'logout' %}" class="logout-form">
                {% csrf_token %}
                <button type="submit" class="logout-btn">
                  <i class="fas fa-sign-out-alt me-2"></i>Logout
                </button>
              </form>
            </div>
          </div>
        {% else %}
          <a href="{% url 'login' %}" class="btn btn-outline-light me-2">
            <i class="fas fa-sign-in-alt me-1"></i>Login
          </a>
          <a href="{% url 'register' %}" class="btn btn-primary">
            <i class="fas fa-user-plus me-1"></i>Register
          </a>
        {% endif %}
      </div>
    </div>
  </div>
</nav>
    <div class="container">
      {% block content %}
      {% endblock %}
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Initialize Bootstrap dropdowns -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize all dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
          return new bootstrap.Dropdown(dropdownToggleEl);
        });

        console.log('Bootstrap dropdowns initialized:', dropdownList.length);
      });
    </script>
</body>
</html>
