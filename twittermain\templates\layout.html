{% load static %}

<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
  <link rel="stylesheet" href="{% static 'style.css' %}">
  <title>
    {% block title %}
      Twitter
    {% endblock %}
  </title>
</head>
<body>
  <nav class="navbar navbar-expand-lg bg-body-tertiary">
  <div class="container-fluid">
    <a class="navbar-brand" href="#">Twitter</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav me-auto mb-2 mb-lg-0">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page" href="{% url 'tweet_list' %}">Home</a>
        </li>
      </ul>
    </div>
    {% if user.is_authenticated %}
    <form method="post" action="{% url 'logout' %}">
      {% csrf_token %}
      <button type="submit" class="btn btn-outline-success">Logout</button>
    </form>
    {% else %}
    <a href="{% url 'login' %}" class="btn btn-outline-success">Login</a>
    <a href="{% url 'register' %}" class="btn btn-outline-success">Register</a>
    {% endif %}
  </div>
</nav>
    <div class="container">
      {% block content %}
      {% endblock %}
    </div>
</body>
</html>
